import { router } from 'expo-router';
import { MealCard } from '@/components/track-meals/history-meal-card';
import { MealCardProps } from '@/components/track-meals/types';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { SegmentedControl } from '@/components/ui/segmented-control';
import { CustomText } from '@/components/ui/text';
import { mealHistory } from '@/constants/meal-history';
import { SavedMealItem, savedMeals } from '@/constants/saved-meals';
import { useMemo, useState } from 'react';
import { SectionList, View } from 'react-native';

interface MealWithTracking extends MealCardProps {
  isUntracked?: boolean;
}

interface GroupedMealData {
  title: string;
  data: MealWithTracking[];
  totalCalories: number;
}

const groupMealsByDate = (
  trackedMeals: MealCardProps[],
  untrackedMeals: SavedMealItem[]
): GroupedMealData[] => {
  // Combine tracked and untracked meals
  const allMeals: MealWithTracking[] = [
    ...trackedMeals.map((meal) => ({ ...meal, isUntracked: false })),
    ...untrackedMeals.map((meal) => ({ ...meal, isUntracked: true })),
  ];

  const groupedMeals = allMeals.reduce(
    (acc, meal) => {
      const date = new Date(meal.timestamp).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(meal);
      return acc;
    },
    {} as Record<string, MealWithTracking[]>
  );

  return Object.keys(groupedMeals).map((date) => {
    const mealsForDate = groupedMeals[date];
    const totalCalories = mealsForDate.reduce((sum, meal) => {
      const calorieValue = parseInt(meal.calories.toString());
      return sum + (isNaN(calorieValue) ? 0 : calorieValue);
    }, 0);

    return {
      title: date,
      data: mealsForDate,
      totalCalories,
    };
  });
};

const TrackMeals = () => {
  const [filterType, setFilterType] = useState<'All' | 'Tracked' | 'Untracked'>('All');
  const allGroupedData = useMemo(() => groupMealsByDate(mealHistory, savedMeals), []);

  // Filter data based on selected filter
  const filteredGroupedData = useMemo(() => {
    return allGroupedData
      .map((section) => ({
        ...section,
        data: section.data.filter((meal) => {
          if (filterType === 'All') return true;
          if (filterType === 'Tracked') return !meal.isUntracked;
          if (filterType === 'Untracked') return meal.isUntracked;
          return true;
        }),
      }))
      .filter((section) => section.data.length > 0);
  }, [filterType, allGroupedData]);

  const handleAddMeal = () => {
    router.push('/add-to-log');
  };

  return (
    <SafeAreaContainer>
      <View className="flex-1">
        <CustomText variant={'h2'} className="mb-1 text-black">
          Meal History
        </CustomText>
        <CustomText className="mb-6 text-muted-foreground">
          Track your daily meals and nutrition
        </CustomText>

        {/* Filter Segmented Control */}
        <View className="mb-4">
          <SegmentedControl
            options={['All', 'Tracked', 'Untracked']}
            value={filterType}
            onValueChange={(value) => setFilterType(value as 'All' | 'Tracked' | 'Untracked')}
          />
        </View>

        {filteredGroupedData.length === 0 ? (
          <View className="flex-1 items-center justify-center">
            <CustomText className="text-center text-muted-foreground">
              No {filterType === 'All' ? 'meals' : filterType.toLowerCase() + ' meals'} found
            </CustomText>
          </View>
        ) : (
          <SectionList
            sections={filteredGroupedData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item) => item.mealName + item.timestamp}
            renderItem={({ item }) => (
              <MealCard
                {...item}
                isUntracked={item.isUntracked}
                onAddMeal={item.isUntracked ? handleAddMeal : undefined}
              />
            )}
            renderSectionHeader={({ section: { title, totalCalories } }) => (
              <View className="mb-3 mt-4 flex flex-row items-center justify-between">
                <CustomText variant={'h4'} className="font-poppins text-primary">
                  {title}
                </CustomText>
                <View className="rounded-full bg-muted px-3 py-1">
                  <CustomText className="text-sm font-medium text-accent">
                    {totalCalories} kcal
                  </CustomText>
                </View>
              </View>
            )}
            ItemSeparatorComponent={() => <View className="h-3" />}
          />
        )}
      </View>
    </SafeAreaContainer>
  );
};

export default TrackMeals;
