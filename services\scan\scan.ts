import apiClient from '@/lib/axios';
import { useMutation, type UseMutationOptions } from '@tanstack/react-query';
import { ScanRequest, ScanResponse } from './types';

export const useScan = (
  options?: Omit<UseMutationOptions<ScanResponse, Error, ScanRequest>, 'mutationFn'>
) =>
  useMutation<ScanResponse, Error, ScanRequest>({
    mutationFn: async (data: ScanRequest) => {
      const formData = new FormData();
      formData.append('image_base64', data.image_base64);

      const response = await apiClient.post('/api/v1/scan/food/analyze/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log(response.data, 'response data');
      return response.data;
    },
    ...options,
  });
