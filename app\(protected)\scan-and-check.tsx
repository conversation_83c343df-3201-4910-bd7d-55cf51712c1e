import { CameraView, useCameraPermissions } from 'expo-camera';
import { useRouter } from 'expo-router';
import React, { useRef, useState } from 'react';
import { Image, Modal, View } from 'react-native';

import ImageCapturedPopup from '@/components/scan-and-check/scan-retake-dialog';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';

const EatableScan = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [modalVisible, setModalVisible] = useState(true);
  const [uri, setUri] = useState<string | null>(null);

  const router = useRouter();

  const ref = useRef<CameraView>(null);

  console.log('uri', uri);

  if (!permission) {
    return (
      <SafeAreaContainer className="flex-1 items-center justify-center bg-white">
        <CustomText variant="default">Checking camera permissions...</CustomText>
      </SafeAreaContainer>
    );
  }

  const takePicture = async () => {
    const photo = await ref.current?.takePictureAsync({
      base64: true,
      shutterSound: false,
    });
    if (photo?.uri) setUri(photo.uri);
  };

  const renderPicture = (uri: string) => {
    if (uri)
      return (
        <View className="absolute bottom-8 left-3 flex-1 items-center justify-center bg-white">
          <Image
            className="border-2 border-white"
            source={{ uri }}
            style={{ width: 100, aspectRatio: 1 }}
          />
        </View>
      );
  };

  const handleProceed = () => {
    router.push('/dashboard');
  };

  if (!permission.granted) {
    return (
      <SafeAreaContainer className="flex-1 items-center justify-center bg-white">
        <Modal
          transparent
          visible={modalVisible}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}>
          <View className="flex-1 items-center justify-center bg-black/40">
            <View className="mx-6 max-w-[80vw] rounded-2xl bg-white p-6 shadow-md">
              <CustomText className="mb-4 text-center text-base">
                To capture photos, please allow access to your device camera.
              </CustomText>

              <View className="flex-row gap-x-3">
                <CustomButton
                  className="flex-1"
                  buttonVariant="secondary"
                  onPress={() => setModalVisible(false)}>
                  <CustomText className="text-accent" variant="buttonText">
                    Cancel
                  </CustomText>
                </CustomButton>

                <CustomButton
                  className="flex-1"
                  buttonVariant="primary"
                  onPress={requestPermission}>
                  <CustomText variant="buttonText">Allow</CustomText>
                </CustomButton>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaContainer>
    );
  }

  return (
    <View className="flex-1">
      {/* using style here because classname doesnt work */}
      <CameraView ref={ref} style={{ flex: 1 }} />
      <CustomButton
        className="bg-tranparent absolute bottom-10 left-[40vw] size-20 rounded-full border-[5px] border-white"
        onPress={takePicture}
      />
      {uri && renderPicture(uri)}
      <ImageCapturedPopup visible={!!uri} onProceed={handleProceed} onRetake={() => setUri(null)} />
    </View>
  );
};

export default EatableScan;
