import { CameraCapturedPicture, CameraView, useCameraPermissions } from 'expo-camera';
import { useFocusEffect, useRouter } from 'expo-router';
import { useCallback, useRef, useState } from 'react';
import { Image, Linking, View } from 'react-native';
import { Image as ImageCompressor } from 'react-native-compressor';

import ImageCapturedPopup from '@/components/scan-and-check/scan-retake-dialog';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useScan } from '@/services/scan/scan';
import { showToast } from 'react-native-nitro-toast';

const EatableScan = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [continueModalVisible, setContinueModalVisible] = useState(false);
  const [photo, setPhoto] = useState<CameraCapturedPicture | undefined>(undefined);
  const [cameraActive, setCameraActive] = useState(true);

  const router = useRouter();

  useFocusEffect(
    useCallback(() => {
      if (!permission?.granted) {
        requestPermission();
      }
      setCameraActive(true);
      return () => {
        setCameraActive(false);
      };
    }, [permission?.granted, requestPermission])
  );

  const ref = useRef<CameraView>(null);

  const { mutate, isPending } = useScan({
    onSuccess(data) {
      router.push({
        pathname: '/scan-results',
        params: {
          data: JSON.stringify(data),
          imageURI: photo?.uri,
        },
      });
    },
    onError(error) {
      showToast(error.message, {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: error.name,
        duration: 14000,
      });
    },
  });

  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({
        base64: true,
        shutterSound: false,
      });
      if (photo?.uri) {
        setPhoto(photo);
        setContinueModalVisible(true);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      showToast('Failed to take picture', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: 'Camera Error',
      });
    }
  };

  const handleProceed = async () => {
    setContinueModalVisible(false);
    if (photo && photo.uri) {
      const compressedImage = await ImageCompressor.compress(photo.uri, {
        compressionMethod: 'auto',
        returnableOutputType: 'base64',
      });
      mutate({ image_base64: compressedImage });
    }
  };

  if (!permission) {
    return (
      <SafeAreaContainer className="flex-1 items-center justify-center bg-white">
        <CustomText variant="default">Checking camera permissions...</CustomText>
      </SafeAreaContainer>
    );
  }

  if (!permission.granted && !permission.canAskAgain) {
    return (
      <SafeAreaContainer className="h-auto w-full flex-1 items-center justify-center">
        <View className="flex w-full items-center justify-center gap-3 rounded-xl bg-card p-3">
          <CustomText className="text-center text-primary" variant="h4">
            This app needs permission to your camera.
          </CustomText>
          <CustomButton>
            <CustomText
              onPress={() => {
                if (permission.canAskAgain) {
                  requestPermission();
                } else {
                  Linking.openSettings();
                }
              }}
              variant={'buttonText'}>
              Give Camera Permission
            </CustomText>
          </CustomButton>
        </View>
      </SafeAreaContainer>
    );
  }

  return (
    <View className="flex-1">
      <View className="flex-1">
        {isPending ? (
          <View className="m-auto flex size-80 flex-col items-center justify-center rounded-xl ">
            <CustomText>Loading...</CustomText>
          </View>
        ) : (
          <>
            <CameraView ref={ref} style={{ flex: 1 }} active={cameraActive} />
            <CustomButton
              className="bg-tranparent absolute bottom-10 left-[40vw] size-20 rounded-full border-[5px] border-white"
              onPress={takePicture}
            />
          </>
        )}
        {photo && photo.uri && continueModalVisible && (
          <View className="absolute bottom-8 left-3 flex-1 items-center justify-center bg-white">
            <Image
              className="border-2 border-white"
              source={{ uri: photo.uri }}
              style={{ width: 100, aspectRatio: 1 }}
            />
          </View>
        )}

        <ImageCapturedPopup
          visible={continueModalVisible}
          onProceed={handleProceed}
          onRetake={() => {
            setPhoto(undefined);
            setContinueModalVisible(false);
          }}
        />
      </View>
    </View>
  );
};

export default EatableScan;
