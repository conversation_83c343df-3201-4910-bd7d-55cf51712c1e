import MacroIndicators from '@/components/dashboard/macro-indicators';
import PetCard from '@/components/dashboard/pet-card';
import RecentlyEaten from '@/components/dashboard/recently-eaten';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { useGetMe } from '@/services/auth/me';
import { Link } from 'expo-router';
import { ScrollView, View } from 'react-native';
import AnimatedProgressWheel from 'react-native-progress-wheel';

const DashboardScreen = () => {
  const { data } = useGetMe({});
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', { day: 'numeric', month: 'long' });

  return (
    <SafeAreaContainer className=" pb-0">
      {/* TODO: temp logout button */}
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        <View className="flex-row justify-between">
          <View>
            <CustomText className="font-poppins text-foreground" variant="h1">
              Hello, {data?.first_name}
            </CustomText>
            <CustomText className="font-lexend text-primary">Today, {formattedDate}</CustomText>
          </View>

          <Link
            href="/profile"
            className="size-12 rounded-full bg-accent pt-2 text-center text-2xl text-white">
            {data?.first_name[0]}
          </Link>
        </View>

        <View className="mb-5 mt-10 items-center justify-center">
          <AnimatedProgressWheel
            size={260}
            width={30}
            color="#006d77"
            progress={1200}
            max={2000}
            rounded
            rotation="-90deg"
            showProgressLabel
            labelStyle={{
              fontFamily: 'LexendDeca_700Bold',
              fontSize: 24,
            }}
            subtitle="/2000 kcal"
            subtitleStyle={{
              fontSize: 16,
              maxWidth: 200,
              fontFamily: 'LexendDeca_400Regular',
              textAlign: 'center',
            }}
            backgroundColor="#83c5be"
          />
        </View>

        <MacroIndicators />

        <PetCard />

        <RecentlyEaten />
      </ScrollView>
    </SafeAreaContainer>
  );
};

export default DashboardScreen;
