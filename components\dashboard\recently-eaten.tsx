import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CustomText } from '../ui/text';

interface RecentlyEatenProps {
  name: string;
  kcal: string | number;
  mealType: string;
}

const RecentlyEatenItem = ({ name, kcal, mealType }: RecentlyEatenProps) => (
  <View className="mx-2 my-2 flex-row items-center rounded-2xl bg-white p-3">
    {/* Icon */}
    <View className="mr-3 rounded-full bg-slate-200 p-3">
      <Ionicons name="fast-food-outline" size={24} color="#25898B" />
    </View>
    {/* Info column */}
    <View className="flex-1">
      <Text className="font-poppins text-base font-medium text-gray-900">{name}</Text>
      <Text className="mt-1 font-lexend text-xs text-teal-600">{mealType}</Text>
    </View>
    {/* Kcal */}
    <Text className="font-lexend text-base text-gray-900">{kcal} kcal</Text>
  </View>
);

const RecentlyEaten = () => {
  return (
    <View className="mt-5">
      <CustomText variant="h3">Recently Eaten</CustomText>
      <RecentlyEatenItem name="Chicken Salad" kcal={350} mealType="Lunch" />
      <RecentlyEatenItem name="Chicken Salad" kcal={350} mealType="Lunch" />
      <RecentlyEatenItem name="Chicken Salad" kcal={350} mealType="Lunch" />
    </View>
  );
};

export default RecentlyEaten;
