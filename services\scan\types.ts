export interface ScanResponse {
  advice: string;
  brand: string | null | number;
  calories: string | null | number;
  food_name: string | null | number;
  is_eatable: string;
  macros: Macros;
  micros: Micros;
  serving: Serving;
  source: string;
}

export interface Macros {
  carbs_g: string | null | number;
  fat_g: string | null | number;
  fiber_g: string | null | number;
  protein_g: string | null | number;
  saturated_fat_g: string | null | number;
  sugar_g: string | null | number;
  unsaturated_fat_g: string | null | number;
}

export interface Micros {
  calcium_mg: string | null | number;
  cholesterol_mg: string | null | number;
  iron_mg: string | null | number;
  potassium_mg: string | null | number;
  sodium_mg: string | null | number;
  vitamin_c_mg: string | null | number;
}

export interface Serving {
  quantity: string | null | number;
  unit: string | null | number;
  weight_grams: string | null | number;
}

export interface ScanRequest {
  image_base64: string;
}
