export interface GoogleAuthResponse {
  access: string;
  refresh: string;
  user: User;
}

export interface User {
  email: string;
  first_name: string;
  last_name: string;
  is_onboarded: boolean;
}

export interface OnboardedUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  age: number;
  gender: string;
  weight: string;
  height: string;
  dietary_preference: string;
  bmi: string;
  goal: string;
  food_intolerances: string;
  last_login: string;
  is_onboarded: boolean;
}

export interface OnboardingPayload {
  age: number;
  gender: 'male' | 'female' | 'others';
  height: string;
  weight: string;
  dietary_preference: 'vegetarian' | 'non_vegetarian';
  goal: 'eat_healthy' | 'lose_weight' | 'gain_muscle' | 'maintain_weight';
  food_intolerances: string;
}

export interface OnboardingResponse {
  age: number;
  gender: string;
  height: string;
  weight: string;
  bmi: string;
  dietary_preference: string;
  goal: string;
  food_intolerances: string;
}
