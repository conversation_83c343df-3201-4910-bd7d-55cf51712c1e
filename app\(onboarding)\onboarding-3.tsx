import NextButton from '@/components/onboarding/next-button';
import OnboardingBars from '@/components/onboarding/onboarding-bars';
import Form from '@/components/ui/form';
import CustomInput from '@/components/ui/input';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { Option, Select } from '@/components/ui/select';
import { CustomText } from '@/components/ui/text';
import { goalOptions } from '@/constants/onboarding';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useOnboarding } from '@/services/auth/onboarding';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { Switch, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';
import * as z from 'zod';

const formSchema = z.object({
  intolerances: z.string().optional(),
  isVegetarian: z.boolean(),
  goal: z.enum(['eat_healthy', 'lose_weight', 'gain_muscle', 'maintain_weight']),
});

type FormSchema = z.infer<typeof formSchema>;

const OnBoarding = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      intolerances: '',
      isVegetarian: false,
      goal: 'lose_weight',
    },
  });

  const { mutate, isPending } = useOnboarding({
    onSuccess() {
      router.push('/onboarding-4');
    },

    onError() {
      showToast('Error updating info!', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
      });
    },
  });

  const onSubmit = async (data: FormSchema) => {
    mutate({
      ...data,
      dietary_preference: data.isVegetarian ? 'vegetarian' : 'non_vegetarian',
    });
  };

  return (
    <SafeAreaContainer className="flex flex-1 items-center justify-start pt-0">
      <Form>
        <View className="max-h-screen-safe flex-1">
          <OnboardingBars barsFilled={3} />
          <View className="gap-y-3">
            <CustomText className="ios:text-5xl ios:pt-3 self-start font-poppins-bold text-3xl text-foreground">
              Any dietary Restrictions?
            </CustomText>
            <CustomText className="self-start font-lexend text-lg text-accent-foreground">
              This helps us personalise the experience for you
            </CustomText>
          </View>

          <View className="my-4 flex w-full flex-col gap-3">
            <CustomText className="ml-2 self-start text-sm">Food intolerances (if any)</CustomText>
            <Controller
              control={control}
              name="intolerances"
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomInput
                  inputType="primary"
                  numberOfLines={100}
                  multiline
                  className="h-48  placeholder:mb-auto"
                  style={{ textAlignVertical: 'top' }}
                  placeholder="Enter your intolerances (optional)"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value?.toString() ?? ''}
                />
              )}
            />
            {errors.intolerances && (
              <CustomText variant={'error'}>{errors.intolerances.message}</CustomText>
            )}
            <View className="mt-3 flex h-12 w-full flex-row items-center justify-between rounded-xl bg-card px-5">
              <CustomText className="my-auto self-start text-sm">Are you a vegetarian?</CustomText>
              <Controller
                control={control}
                name="isVegetarian"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Switch
                    thumbColor={'#006d77'}
                    className="my-auto"
                    trackColor={{ true: '#83c5be', false: '#ddd' }}
                    ios_backgroundColor={'#ddd'}
                    onBlur={onBlur}
                    onValueChange={onChange}
                    value={value}
                  />
                )}
              />
            </View>
            {errors.isVegetarian && (
              <CustomText variant={'error'}>{errors.isVegetarian.message}</CustomText>
            )}

            <View className="mt-3 h-fit w-full rounded-xl bg-card px-5 pt-5">
              <CustomText className="self-start text-sm">What is your weight goal?</CustomText>
              <Controller
                control={control}
                name="goal"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Select
                    itemStyle={{ height: 120 }}
                    onValueChange={onChange}
                    onBlur={onBlur}
                    selectedValue={value}>
                    {goalOptions.map((option) => (
                      <Option key={option.value} value={option.value} label={option.label} />
                    ))}
                  </Select>
                )}
              />
              {errors.goal && <CustomText variant={'error'}>{errors.goal.message}</CustomText>}
            </View>
          </View>

          <NextButton
            buttonTitle={isPending ? 'Loading...' : 'Next'}
            disabled={isPending}
            onPress={handleSubmit(onSubmit)}
          />
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default OnBoarding;
