import { View } from 'react-native';
import ProgressBar from '../ui/progress-bar';
import { CustomText } from '../ui/text';

const MacroIndicators = () => {
  return (
    <View className="gap-y-3">
      <CustomText variant="h3">Macros</CustomText>
      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Protein</CustomText>
          <CustomText>39/100g</CustomText>
        </View>
        <ProgressBar progress={39 / 100} />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Carbs</CustomText>
          <CustomText>85/100g</CustomText>
        </View>
        <ProgressBar progress={85 / 100} />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Fats</CustomText>
          <CustomText>30/100g</CustomText>
        </View>
        <ProgressBar progress={30 / 100} />
      </View>
    </View>
  );
};

export default MacroIndicators;
