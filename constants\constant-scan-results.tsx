import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { EatableInfoBoxProps } from '@/components/scan-and-check/eatable-info';
import { Macros } from '@/services/scan/types';
import { JSX } from 'react';

export type MACRO_ICON_PROPS = {
  [K in keyof Macros]: JSX.Element;
};

export const MACRO_ICONS: MACRO_ICON_PROPS = {
  protein_g: <FontAwesome6 name="dumbbell" size={18} color="#3B82F6" />,
  carbs_g: <FontAwesome6 name="bowl-rice" size={18} color="#F59E0B" />,
  fiber_g: <FontAwesome6 name="leaf" size={18} color="#8BC34A" />,
  fat_g: <FontAwesome6 name="burger" size={18} color="#F97316" />,
  saturated_fat_g: <FontAwesome6 name="cheese" size={18} color="#EAB308" />, // yellow (solid fat)
  unsaturated_fat_g: <FontAwesome6 name="olive-branch" size={18} color="#65A30D" />, // olive oil green
  sugar_g: <FontAwesome6 name="candy-cane" size={18} color="#EF4444" />, // red (sweet)
};

// NOTE: FOLLOWING FROM HERE IS A TEMPORARY MOCK DATA

export const eatableResults: EatableInfoBoxProps = {
  type: 'success',
  title: 'Yes',
  message: 'This fits well within your daily nutritional goals. Enjoy!',
};

export const notEatableResults: EatableInfoBoxProps = {
  type: 'warning',
  title: 'No',
  message: 'Based on your weight loss goal, a portion size of 100g is recommended.',
};
